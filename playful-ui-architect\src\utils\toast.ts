import { toast as sonnerToast } from 'sonner';

const TOAST_STYLES = {
  error: { backgroundColor: '#FFFFFF', color: '#EF4444' },
  success: { backgroundColor: '#FFFFFF', color: '#16A34A' },
  successAlt: { backgroundColor: '#FFFFFF', color: '#22C55E' },
  info: { backgroundColor: '#FFFFFF', color: '#F97316' },
  warning: { backgroundColor: '#FFFFFF', color: '#F59E0B' },
} as const;

const TOAST_DURATIONS = {
  short: 2000,
  medium: 3000,
  long: 5000,
} as const;

const DEFAULT_OPTIONS = {
  dismissible: true,
} as const;

interface ToastOptions {
  duration?: number;
  dismissible?: boolean;
  style?: Record<string, string>;
}

interface ToastUtility {
  error: (message: string, options?: ToastOptions) => string | number;
  success: (message: string, options?: ToastOptions) => string | number;
  info: (message: string, options?: ToastOptions) => string | number;
  warning: (message: string, options?: ToastOptions) => string | number;
  loading: (message: string, options?: ToastOptions) => string | number;
  dismiss: (toastId: string | number) => void;
  promise: <T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string;
      error: string | ((error: unknown) => string);
    },
    options?: ToastOptions
  ) => Promise<T>;
}

export const toast: ToastUtility = {
  error: (message: string, options: ToastOptions = {}) => {
    return sonnerToast.error(message, {
      style: TOAST_STYLES.error,
      duration: TOAST_DURATIONS.medium,
      ...DEFAULT_OPTIONS,
      ...options,
    });
  },

  success: (message: string, options: ToastOptions = {}) => {
    return sonnerToast.success(message, {
      style: {
        backgroundColor: '#FFFFFF !important',
        color: '#16A34A !important',
        borderColor: '#16A34A !important',
      },
      duration: TOAST_DURATIONS.short,
      ...DEFAULT_OPTIONS,
      ...options,
    });
  },

  info: (message: string, options: ToastOptions = {}) => {
    return sonnerToast.info(message, {
      style: TOAST_STYLES.info,
      duration: TOAST_DURATIONS.medium,
      ...DEFAULT_OPTIONS,
      ...options,
    });
  },

  warning: (message: string, options: ToastOptions = {}) => {
    return sonnerToast.warning(message, {
      style: TOAST_STYLES.warning,
      duration: TOAST_DURATIONS.medium,
      ...DEFAULT_OPTIONS,
      ...options,
    });
  },

  loading: (message: string, options: ToastOptions = {}) => {
    return sonnerToast.loading(message, {
      style: TOAST_STYLES.info,
      duration: TOAST_DURATIONS.long,
      ...DEFAULT_OPTIONS,
      ...options,
    });
  },

  dismiss: (toastId: string | number) => {
    sonnerToast.dismiss(toastId);
  },

  promise: <T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string;
      error: string | ((error: unknown) => string);
    }
  ): Promise<T> => {
    sonnerToast.promise(promise, messages);
    return promise;
  },
};

export const toastUtils = {
  successAlt: (message: string, options: ToastOptions = {}) => {
    return sonnerToast.success(message, {
      style: TOAST_STYLES.successAlt,
      duration: TOAST_DURATIONS.short,
      ...DEFAULT_OPTIONS,
      ...options,
    });
  },

  authError: (message: string = 'Authentication failed. Please try again.') => {
    return toast.error(message);
  },

  networkError: (
    message: string = 'Network error. Please check your connection.'
  ) => {
    return toast.error(message);
  },

  serverError: (message: string = 'Server error. Please try again later.') => {
    return toast.error(message);
  },

  validationError: (
    message: string = 'Please check your input and try again.'
  ) => {
    return toast.error(message);
  },

  retryInfo: (action: string) => {
    return toast.info(`Retrying to ${action}...`);
  },
};

export { TOAST_STYLES, TOAST_DURATIONS, DEFAULT_OPTIONS };
