import { toast as sonnerToast } from 'sonner';

const TOAST_DURATIONS = {
  short: 2000,
  medium: 3000,
  long: 5000,
} as const;

const DEFAULT_OPTIONS = {
  dismissible: true,
} as const;

interface ToastOptions {
  duration?: number;
  dismissible?: boolean;
  style?: Record<string, string>;
}

interface ToastUtility {
  error: (message: string, options?: ToastOptions) => string | number;
  success: (message: string, options?: ToastOptions) => string | number;
  info: (message: string, options?: ToastOptions) => string | number;
  warning: (message: string, options?: ToastOptions) => string | number;
  loading: (message: string, options?: ToastOptions) => string | number;
  dismiss: (toastId: string | number) => void;
  promise: <T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string;
      error: string | ((error: unknown) => string);
    },
    options?: ToastOptions
  ) => Promise<T>;
}

export const toast: ToastUtility = {
  error: (message: string, options: ToastOptions = {}) => {
    return sonnerToast.error(message, {
      style: {
        backgroundColor: '#FFFFFF !important',
        color: '#EF4444 !important',
        borderColor: '#EF4444 !important',
      },
      duration: TOAST_DURATIONS.medium,
      ...DEFAULT_OPTIONS,
      ...options,
    });
  },

  success: (message: string, options: ToastOptions = {}) => {
    return sonnerToast.success(message, {
      style: {
        backgroundColor: '#FFFFFF !important',
        color: '#16A34A !important',
        borderColor: '#16A34A !important',
      },
      duration: TOAST_DURATIONS.short,
      ...DEFAULT_OPTIONS,
      ...options,
    });
  },

  info: (message: string, options: ToastOptions = {}) => {
    return sonnerToast.info(message, {
      style: {
        backgroundColor: '#FFFFFF !important',
        color: '#F97316 !important',
        borderColor: '#F97316 !important',
      },
      duration: TOAST_DURATIONS.medium,
      ...DEFAULT_OPTIONS,
      ...options,
    });
  },

  warning: (message: string, options: ToastOptions = {}) => {
    return sonnerToast.warning(message, {
      style: {
        backgroundColor: '#FFFFFF !important',
        color: '#F59E0B !important',
        borderColor: '#F59E0B !important',
      },
      duration: TOAST_DURATIONS.medium,
      ...DEFAULT_OPTIONS,
      ...options,
    });
  },

  loading: (message: string, options: ToastOptions = {}) => {
    return sonnerToast.loading(message, {
      style: {
        backgroundColor: '#FFFFFF !important',
        color: '#F97316 !important',
        borderColor: '#F97316 !important',
      },
      duration: TOAST_DURATIONS.long,
      ...DEFAULT_OPTIONS,
      ...options,
    });
  },

  dismiss: (toastId: string | number) => {
    sonnerToast.dismiss(toastId);
  },

  promise: <T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string;
      error: string | ((error: unknown) => string);
    }
  ): Promise<T> => {
    sonnerToast.promise(promise, messages);
    return promise;
  },
};

export const toastUtils = {
  successAlt: (message: string, options: ToastOptions = {}) => {
    return sonnerToast.success(message, {
      style: {
        backgroundColor: '#FFFFFF !important',
        color: '#22C55E !important',
        borderColor: '#22C55E !important',
      },
      duration: TOAST_DURATIONS.short,
      ...DEFAULT_OPTIONS,
      ...options,
    });
  },

  authError: (message: string = 'Authentication failed. Please try again.') => {
    return toast.error(message);
  },

  networkError: (
    message: string = 'Network error. Please check your connection.'
  ) => {
    return toast.error(message);
  },

  serverError: (message: string = 'Server error. Please try again later.') => {
    return toast.error(message);
  },

  validationError: (
    message: string = 'Please check your input and try again.'
  ) => {
    return toast.error(message);
  },

  retryInfo: (action: string) => {
    return toast.info(`Retrying to ${action}...`);
  },
};

export { TOAST_DURATIONS, DEFAULT_OPTIONS };
